{"name": "quotelier-new-request", "version": "1.1.0", "private": true, "scripts": {"deploy": "sls deploy -s $STAGE -r $REGION --domain $DOMAIN --subdomain $STAGE --verbose", "shutdown": "sls remove -s $STAGE -r $REGION --domain $DOMAIN --subdomain $STAGE --verbose", "invoke": "sls invoke -s $STAGE -r $REGION --domain $DOMAIN --subdomain $STAGE --verbose", "test": "NODE_ENV=test mocha --recursive --require ./test/bootstrap.js -t 6000", "mocha": "NODE_ENV=test mocha --recursive --require ./test/bootstrap.js -t 6000"}, "devDependencies": {"@types/chai": "^4.0.5", "@types/joi": "^10.6.0", "@types/mocha": "^2.2.44", "@types/nock": "^8.2.1", "@types/sinon": "^2.3.7", "aws-sdk": "^2.176.0", "chai": "^4.1.2", "chai-subset": "^1.6.0", "mocha": "^3.5.3", "nock": "^9.1.3", "sinon": "^2.4.1"}, "dependencies": {"@joi/date": "^2.1.0", "@quotelier/handlebars": "file:../../packages/handlebars", "@quotelier/invokeLambda": "file:../../packages/invokeLambda", "@quotelier/logger": "file:../../packages/logger", "accounting": "^0.4.1", "bluebird": "^3.5.1", "dynamodb-data-types": "^3.0.0", "handlebars": "^4.0.11", "joi": "^17.4.2", "jsonschema": "^1.2.4", "lodash": "^4.17.4", "lodash-deep": "^2.0.0", "middy": "^0.36.0", "mjml": "^4.10.3", "moment": "^2.19.2", "moment-range": "^4.0.2", "ms": "^2.1.3", "raven": "^2.6.4"}}
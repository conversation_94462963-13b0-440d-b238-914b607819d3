service: new-request

provider:
  name: aws
  runtime: nodejs16.x
  profile: quotelier
  cfLogs: true
  timeout: 29
  versionFunctions: false
  deploymentBucket:
    name: quotelier-serverless-deployment-bucket
  environment:
    SERVICE: ${self:service}
    RELEASE: ${env:RELEASE, 'none'}
    SENTRY_DNS: ${env:SENTRY_DNS}
    SENTRY_CAPTURE: 0
    STAGE: ${opt:stage}
    REQUESTS_TABLE:
      Ref: SSERequestsTable
    REQUESTS_LOCK_TIMEOUT: 60
    REQUESTS_LOCK_TABLE:
      Ref: RequestsLockTable
    PROPOSALS_BUCKET:
      Ref: ProposalsBucket
    PUBLISHED_REQUEST_BUCKET_NAME:
      Ref: ProposalsBucket
    DOMAIN: ${opt:domain}
    EMAIL_FUNCTION_NAME: notifications-${opt:stage}-sendEmail
    CREATE_RESERVATION_FUNCTION_NAME: integrations-${opt:stage}-createReservation
    GET_AVAILABILITY_FUNCTION_NAME: integrations-${opt:stage}-getAvailability
    GET_RESERVATION_FUNCTION_NAME: integrations-${opt:stage}-getReservation
    CANCEL_RESERVATION_FUNCTION_NAME: integrations-${opt:stage}-cancelReservation
    PURGE_RESERVATION_FUNCTION_NAME: integrations-${opt:stage}-purgeReservation
    CONFIRM_RESERVATION_FUNCTION_NAME: integrations-${opt:stage}-confirmReservation
    GET_TEMPLATE_FUNCTION: properties-service-${opt:stage}-getTemplate
    GET_TEMPLATES_FUNCTION: properties-service-${opt:stage}-getTemplates
    GET_PARTIAL_TEMPLATES_FUNCTION: properties-service-${opt:stage}-getPartialTemplates
    GET_EMAIL_TEMPLATE_FUNCTION_NAME: properties-service-${opt:stage}-getEmailTemplate
    REMOVE_SCHEDULER_ENTRY_FUNCTION_NAME: request-scheduler-${opt:stage}-remove
    ADD_SCHEDULER_ENTRY_FUNCTION_NAME: request-scheduler-${opt:stage}-add
    GET_PROPERTY_SETTING_FUNCTION_NAME: properties-service-${opt:stage}-getSetting
    GET_PROPERTY_SETTINGS_FUNCTION_NAME: properties-service-${opt:stage}-getSettings
    GET_PROPERTY_FUNCTION_NAME: properties-service-${opt:stage}-getProperty
    GET_SERVICES_FUNCTION_NAME: integrations-${opt:stage}-getServices
    GET_SERVICE_FUNCTION_NAME: integrations-${opt:stage}-getService
    GET_ROOM_FUNCTION_NAME: integrations-${opt:stage}-getRoom
    GET_RATE_FUNCTION_NAME: integrations-${opt:stage}-getRate
    GET_OPERATOR_FUNCTION_NAME: operators-service-${opt:stage}-getOperator
    ADD_OPERATOR_TAGS_FUNCTION_NAME: operators-service-${opt:stage}-addOperatorTags
    GET_OPERATOR_SETTINGS_FUNCTION_NAME: operators-service-${opt:stage}-getOperatorSettings
    GET_BOARD_FUNCTION_NAME: properties-service-${opt:stage}-getBoard
  iamRoleStatements:
    - Effect: Allow
      Action:
      - lambda:InvokeFunction
      - ec2:*
      - dynamodb:*
      - s3:*
      Resource: "*"

functions:
  renderTemplate:
    handler: handlers/renderTemplate.handler
  markToFollowUpRequest:
    handler: handlers/markToFollowUpRequest.handler
  toggleMuteRequest:
    handler: handlers/toggleMuteRequest.handler
  updateRequestNotes:
    handler: handlers/updateRequestNotes.handler
  unmarkToFollowUpRequest:
    handler: handlers/unmarkToFollowUpRequest.handler
  notifyExpiring:
    handler: handlers/notifyExpiring.handler
  getRequest:
    handler: handlers/getRequest.handler
  extendRequest:
    handler: handlers/extendRequest.handler
  getRequestPossibleActions:
    handler: handlers/getRequestPossibleActions.handler
  createRequest:
    handler: handlers/createRequest.handler
  createNewRequest:
    handler: handlers/createNewRequest.handler
  createWaitingRequest:
    handler: handlers/createWaitingRequest.handler
  updateRequest:
    handler: handlers/updateRequest.handler
  assignRequest:
    handler: handlers/assignRequest.handler
  acceptUnavailableRequest:
    handler: handlers/acceptUnavailableRequest.handler
  cancelUnavailableRequest:
    handler: handlers/cancelUnavailableRequest.handler
  ownRequest:
    handler: handlers/ownRequest.handler
  followupRequest:
    handler: handlers/followupRequest.handler
  revertRequest:
    handler: handlers/revertRequest.handler
  openRequest:
    handler: handlers/openRequest.handler
    events:
      - http:
          path: opened/{requestId}
          method: GET
          integration: lambda
          request:
            template:
              application/json: '{ "requestId" : "$input.params(''requestId'')" }'
            parameters:
              paths:
                requestId: true
          cors: true
  acceptRequestOffer:
    handler: handlers/acceptRequestOffer.handler
    events:
      - http:
          path: accepted/{requestId}/{offerId}
          method: GET
          integration: lambda
          request:
            template:
              application/json: '{"requestId" : "$input.params(''requestId'')","offerId" : "$input.params(''offerId'')"}'
            parameters:
              paths:
                requestId: true
                offerId: true
          cors: true
  acceptRequestOption:
    handler: handlers/acceptRequestOption.handler
    events:
      - http:
          path: accepted/{requestId}/option/{optionId}
          method: GET
          integration: lambda
          request:
            template:
              application/json: '{"requestId" : "$input.params(''requestId'')","optionId" : "$input.params(''optionId'')"}'
            parameters:
              paths:
                requestId: true
                optionId: true
          cors: true
  acceptRequest:
    handler: handlers/acceptRequest.handler
    events:
      - http:
          path: accepted/{requestId}/options/{optionIds}
          method: GET
          integration: lambda
          request:
            template:
              application/json: '{"requestId" : "$input.params(''requestId'')","optionIds" : "$input.params(''optionIds'')"}'
            parameters:
              paths:
                requestId: true
                optionIds: true
          cors: true
  rejectRequest:
    handler: handlers/rejectRequest.handler
    events:
      - http:
          path: rejected/{requestId}
          method: GET
          integration: lambda
          cors: true
          request:
            template:
              application/json: '{"requestId" : "$input.params(''requestId'')", "reasons" : "$input.params(''reasons'')"}'
            parameters:
              paths:
                requestId: true
  confirmRequest:
    handler: handlers/confirmRequest.handler
  expireRequest:
    handler: handlers/expireRequest.handler
  cancelRequest:
    handler: handlers/cancelRequest.handler
  purgeRequest:
    handler: handlers/purgeRequest.handler
  archiveRequest:
    handler: handlers/archiveRequest.handler
  waitlistRequest:
    handler: handlers/waitlistRequest.handler
  reservationCancellation:
    handler: handlers/reservationCancellation.SNSHandler
    timeout: 24
    environment:
      SEARCH_FUNCTION_NAME: indexing-${opt:stage}-search
    events:
      - sns:
          arn: ${cf:integrations-${opt:stage}.ReservationCancellationTopicArn}
  reservationPaymentCardAdded:
    handler: handlers/reservationPaymentCardAdded.SNSHandler
    timeout: 24
    events:
      - sns:
          arn: ${cf:integrations-${opt:stage}.ReservationPaymentCardAddedTopicArn}
  reservationOnlinePayment:
    handler: handlers/reservationOnlinePayment.SNSHandler
    timeout: 24
    events:
      - sns:
          arn: ${cf:integrations-${opt:stage}.ReservationOnlinePaymentTopicArn}
  renderRequest:
    handler: handlers/renderRequest.handler
  sendRequest:
    handler: handlers/sendRequest.handler
    environment:
      REQUEST_LIFECYCLE_URL: ${self:custom.lifecycleEndpoint}
  syncRequestsIndex:
    handler: handlers/syncRequestsIndex.handler
    timeout: 300
    environment:
      INDEX_FUNCTION: indexing-${opt:stage}-index
      POPULATION_CONCURRENCY: "5"
      DYNAMODB_SCAN_LIMIT: "50"
  indexRequest:
    handler: handlers/indexRequest.handler
    events:
      - stream:
          type: dynamodb
          arn:
            Fn::GetAtt:
              - SSERequestsTable
              - StreamArn
          startingPosition: "LATEST"
          batchSize: 5
          enabled: true
    environment:
      INDEX_FUNCTION: indexing-${opt:stage}-index
  deleteProposal:
    handler: handlers/deleteProposal.handler

custom:
  lifecycleEndpoint: "https://api.${opt:domain}/${opt:stage}-lifecycle"

resources:
  Mappings:
    RegionMap:
      us-east-1:
        S3hostedzoneID: Z3AQBSTGFYJSTF
        websiteendpoint: s3-website-us-east-1.amazonaws.com
      us-west-1:
        S3hostedzoneID: Z2F56UZL2M1ACD
        websiteendpoint: s3-website-us-west-1.amazonaws.com
      us-west-2:
        S3hostedzoneID: Z3BJ6K6RIION7M
        websiteendpoint: s3-website-us-west-2.amazonaws.com
      eu-west-1:
        S3hostedzoneID: Z1BKCTXD74EZPE
        websiteendpoint: s3-website-eu-west-1.amazonaws.com
      eu-central-1:
        S3hostedzoneID: Z21DNDUVLTQW6Q
        websiteendpoint: s3-website.eu-central-1.amazonaws.com
      ap-southeast-1:
        S3hostedzoneID: Z3O0J2DXBE1FTB
        websiteendpoint: s3-website-ap-southeast-1.amazonaws.com
      ap-southeast-2:
        S3hostedzoneID: Z1WCIGYICN2BYD
        websiteendpoint: s3-website-ap-southeast-2.amazonaws.com
      ap-northeast-1:
        S3hostedzoneID: Z2M4EHUR26P7ZW
        websiteendpoint: s3-website-ap-northeast-1.amazonaws.com
      sa-east-1:
        S3hostedzoneID: Z31GFT0UA1I2HV
        websiteendpoint: s3-website-sa-east-1.amazonaws.com
  Resources:
    SSERequestsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: "${self:service}-${opt:stage}-sse-requests"
        SSESpecification:
          SSEEnabled: true
        AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        KeySchema:
        - AttributeName: id
          KeyType: HASH
        StreamSpecification:
          StreamViewType: NEW_IMAGE
        ProvisionedThroughput:
          ReadCapacityUnits: 5
          WriteCapacityUnits: 5
    RequestsLockTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: "${self:service}-${opt:stage}-lock-requests"
        AttributeDefinitions:
        - AttributeName: id
          AttributeType: S
        KeySchema:
        - AttributeName: id
          KeyType: HASH
        TimeToLiveSpecification:
          Enabled: true
          AttributeName: 'ttl'
        ProvisionedThroughput:
          ReadCapacityUnits: 5
          WriteCapacityUnits: 5
    ProposalsCloudFrontDistribution:
      Type: AWS::CloudFront::Distribution
      Properties:
        DistributionConfig:
          Origins:
            - DomainName: ${self:service}-${opt:stage}-proposals.s3.amazonaws.com
              Id: ${self:service}-${opt:stage}-proposals
              CustomOriginConfig:
                OriginProtocolPolicy: http-only
            ## This is the upsales origin hard coded. In case of a fresh deployment
            ## you may need to uncomment this until the Upsales service is deployed
            - DomainName: upsales-${opt:stage}-proposals.s3.amazonaws.com
              Id: upsales-${opt:stage}-proposals
              CustomOriginConfig:
                OriginProtocolPolicy: http-only
            - DomainName: quotelier-upgrade-${opt:stage}-proposals.s3.amazonaws.com
              Id: quotelier-upgrade-${opt:stage}-proposals
              CustomOriginConfig:
                OriginProtocolPolicy: http-only
          CacheBehaviors:
            ## This is the upsales origin hard coded. In case of a fresh deployment
            ## you may need to uncomment this until the Upsales service is deployed
            - PathPattern: 'u/*'
              MaxTTL: 0
              MinTTL: 0
              DefaultTTL: 0
              AllowedMethods:
                - HEAD
                - GET
              TargetOriginId: upsales-${opt:stage}-proposals
              Compress: true
              ForwardedValues:
                QueryString: true
                Cookies:
                  Forward: none
              ViewerProtocolPolicy: allow-all
            - PathPattern: 'upg/*'
              MaxTTL: 0
              MinTTL: 0
              DefaultTTL: 0
              AllowedMethods:
                - HEAD
                - GET
              TargetOriginId: quotelier-upgrade-${opt:stage}-proposals
              Compress: true
              ForwardedValues:
                QueryString: true
                Cookies:
                  Forward: none
              ViewerProtocolPolicy: allow-all
          DefaultCacheBehavior:
            Compress: true
            MaxTTL: 0
            MinTTL: 0
            DefaultTTL: 0
            AllowedMethods:
              - HEAD
              - GET
            TargetOriginId: ${self:service}-${opt:stage}-proposals
            ForwardedValues:
              QueryString: true
              Cookies:
                Forward: none
            ViewerProtocolPolicy: allow-all
          Enabled: true
          Aliases:
            - ${opt:subdomain}.${opt:domain}
          CustomErrorResponses:
            - ErrorCode: 403
              ErrorCachingMinTTL: 0
    ProposalsDomainRecordSet:
      Type: "AWS::Route53::RecordSetGroup"
      Properties:
        HostedZoneName: "${opt:domain}."
        RecordSets:
          - Name: ${opt:subdomain}.${opt:domain}
            Type: A
            AliasTarget:
              HostedZoneId: Z2FDTNDATAQYW2 # cloud-front
              DNSName:
                Fn::GetAtt:
                  - ProposalsCloudFrontDistribution
                  - DomainName
    ProposalsBucket:
      Type: AWS::S3::Bucket
      Properties:
        WebsiteConfiguration:
          IndexDocument: index.html
        BucketName: ${self:service}-${opt:stage}-proposals
    ProposalsBucketPolicy:
      Type: AWS::S3::BucketPolicy
      Properties:
        Bucket: ${self:service}-${opt:stage}-proposals
        PolicyDocument:
          Statement:
          - Effect: Allow
            Principal: "*"
            Action: s3:GetObject
            Resource: arn:aws:s3:::${self:service}-${opt:stage}-proposals/*
    LifecycleBasePath:
      # expects an existing configured custom-domain-name with certificate
      # and sub-domain: "api."
      Type : AWS::ApiGateway::BasePathMapping
      Properties:
        BasePath: "${opt:stage}-lifecycle"
        DomainName: api.${opt:domain}
        RestApiId:
          Ref: ApiGatewayRestApi
        Stage: ${opt:stage}
  Outputs:
    SSERequestTableStreamARN:
      Description: RequestTableStreamARN.
      Value:
        Fn::GetAtt : ["SSERequestsTable", "StreamArn"]
      Export:
        Name: ${self:service}-${opt:stage}-sse-request-table-stream-arn
    LifecycleEndpoint:
      Description: The base endpoint of the request lifecycle.
      Value: ${self:custom.lifecycleEndpoint}
      Export:
        Name: new-request-${opt:stage}-lifecycle-endpoint

service: integrations

package:
  exclude:
    - .serverless/**

provider:
  name: aws
  runtime: nodejs16.x
  profile: quotelier
  cfLogs: true
  versionFunctions: false
  deploymentBucket: quotelier-serverless-deployment-bucket
  timeout: 30
  environment:
    SERVICE: ${self:service}
    RELEASE: ${env:RELEASE, 'none'}
    SENTRY_DNS: ${env:SENTRY_DNS}
    SENTRY_CAPTURE_WARNINGS: 0
    INDEXING_SEARCH_FUNCTION: indexing-${opt:stage}-search
    KMS_KEY_ID:
      Ref: KMSKey
    ACCOUNTS_TABLE:
      Ref: AccountsPropertiesTable
    ADAPTOR_PARAMETERS_TABLE_NAME:
      Ref: AdaptorParametersTable
    CACHE_BUCKET_NAME:
      Ref: CacheBucket
    CACHE_EXPIRATION_UNIT: days
    CACHE_EXPIRATION_UNIT_NUM: 7
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
        - dynamodb:*
        - s3:*
        - ssm:*
        - sns:*
      Resource: '*'

functions:
  clearCache:
    handler: handlers/clearCache.handler
  getRoom:
    handler: handlers/getRoom.handler
  getRooms:
    handler: handlers/getRooms.handler
  getService:
    handler: handlers/getService.handler
  getServices:
    handler: handlers/getServices.handler
  getRate:
    handler: handlers/getRate.handler
  getRates:
    handler: handlers/getRates.handler
  createAccount:
    handler: handlers/createAccount.handler
  getWHAccount:
    handler: handlers/getWHAccount.handler
  getAccounts:
    handler: handlers/getAccounts.handler
  authenticateAccount:
    handler: handlers/authenticateAccount.handler
  getAvailability:
    handler: handlers/getAvailability.handler
    environment:
      GET_RATE_FUNCTION_NAME: properties-service-${opt:stage}-getRate
  getAvailabilityBreakdown:
    handler: handlers/getAvailabilityBreakdown.handler
    environment:
      GET_RATE_FUNCTION_NAME: properties-service-${opt:stage}-getRate
  getServicesAvailability:
    handler: handlers/getServicesAvailability.handler
  getProperty:
    handler: handlers/getProperty.handler
  createReservation:
    handler: handlers/createReservation.handler
  updateReservation:
    handler: handlers/updateReservation.handler
  confirmReservation:
    handler: handlers/confirmReservation.handler
  cancelReservation:
    handler: handlers/cancelReservation.handler
  purgeReservation:
    handler: handlers/purgeReservation.handler
  getReservation:
    handler: handlers/getReservation.handler
  getReservations:
    handler: handlers/getReservations.handler
  indexReservation:
    handler: handlers/indexReservation.handler
    events:
      - http:
          path: reservations/{accountName}
          method: POST
          cors: true
          request:
            parameters:
              paths:
                accountName: true
      - http:
          path: reservations
          method: POST
          cors: true
    environment:
      INDEX_FUNCTION: indexing-${opt:stage}-index
      INDEX_DELETE_FUNCTION: indexing-${opt:stage}-delete
      CANCELLATION_TOPIC_ARN:
        Ref: ReservationCancellationTopic
      PAYMENT_CARD_ADDED_TOPIC_ARN:
        Ref: ReservationPaymentCardAddedTopic
      ONLINE_PAYMENT_TOPIC_ARN:
        Ref: ReservationOnlinePaymentTopic
resources:
  Conditions:
    CreateSubscriptions:
      Fn::Equals:
        - ${opt:stage}
        - devel
  Resources:
    IntegrationsApiBasePathMapping:
      Type: AWS::ApiGateway::BasePathMapping
      Properties:
        BasePath: ${opt:stage}-${self:service}
        DomainName: api.${opt:domain}
        RestApiId:
          Ref: ApiGatewayRestApi
        Stage: ${opt:stage}
      # If this fails due to not existing RestAPI or Stage, comment this
      # Resources and deploy once and uncomment and redeploy again
      DependsOn: ApiGatewayRestApi
    KMSKey:
      Type: AWS::KMS::Key
      Properties:
        Enabled: true
        KeyPolicy: '{"Version": "2012-10-17","Statement": [{"Effect": "Allow","Principal": {"AWS": "*"},"Action": ["kms:*"],"Resource": "*"}]}'
    # @TODO:
    # Accounts Table can be deleted after we release and
    # migrate to AccountsPropertiesTable. Just remove this
    # resource.
    AccountsTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:service}-${opt:stage}-accounts
        AttributeDefinitions:
          - AttributeName: accountName
            AttributeType: S
        KeySchema:
          - AttributeName: accountName
            KeyType: HASH
        ProvisionedThroughput:
          ReadCapacityUnits: 5
          WriteCapacityUnits: 5
    AccountsPropertiesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:service}-${opt:stage}-accounts-properties
        AttributeDefinitions:
          - AttributeName: accountName
            AttributeType: S
          - AttributeName: propertyCode
            AttributeType: S
        KeySchema:
          - AttributeName: accountName
            KeyType: HASH
          - AttributeName: propertyCode
            KeyType: RANGE
        ProvisionedThroughput:
          ReadCapacityUnits: 5
          WriteCapacityUnits: 5
    AdaptorParametersTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:service}-${opt:stage}-adaptor-parameters
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: adaptor
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
          - AttributeName: adaptor
            KeyType: RANGE
        ProvisionedThroughput:
          ReadCapacityUnits: 5
          WriteCapacityUnits: 5
    CacheBucket:
      Type: AWS::S3::Bucket
      Properties:
        AccessControl: Private
        BucketName: ${self:service}-${opt:stage}-cache
    ReservationCancellationTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:service}-${opt:stage}-reservation-cancellation-topic
    ReservationPaymentCardAddedTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:service}-${opt:stage}-reservation-payment-card-added-topic
    ReservationOnlinePaymentTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:service}-${opt:stage}-reservation-online-payment-topic
  Outputs:
    ReservationCancellationTopicArn:
      Description: The ARN of the SNS Topic for reservation cancellations
      Value:
        Ref: ReservationCancellationTopic
      Export:
        Name: ${self:service}:${opt:stage}:ReservationCancellationTopicArn
    ReservationPaymentCardAddedTopicArn:
      Description: The ARN of the SNS Topic for reservation payment card add
      Value:
        Ref: ReservationPaymentCardAddedTopic
      Export:
        Name: ${self:service}:${opt:stage}:ReservationPaymentCardAddedTopic
    ReservationOnlinePaymentTopicArn:
      Description: The ARN of the SNS Topic for reservation online payment
      Value:
        Ref: ReservationOnlinePaymentTopic
      Export:
        Name: ${self:service}:${opt:stage}:ReservationOnlinePaymentTopicArn


service: request-scheduler

packages:
  exclude:
    - .serverless/**

provider:
  name: aws
  runtime: nodejs14.x
  profile: quotelier
  cfLogs: true
  versionFunctions: false
  deploymentBucket: quotelier-serverless-deployment-bucket
  iamRoleStatements:
    - Effect: Allow
      Action:
        - lambda:InvokeFunction
      Resource: "*"
    - Effect: Allow
      Action:
        - ec2:*
        - elasticache:*
        - dynamodb:*
      Resource: "*"

  vpc:
    securityGroupIds:
      Fn::If:
        - CreateRedis
        - - ${opt:securityGroupId}
        - - Fn::ImportValue: common-redis-scheduler-security-group
    subnetIds:
      Fn::If:
        - CreateRedis
        - - ${opt:subnetPrivateId}
        - - Fn::ImportValue: common-redis-scheduler-subnet-private
  environment:
    SERVICE: ${self:service}
    RELEASE: ${env:RELEASE, 'none'}
    SENTRY_DNS: ${env:SENTRY_DNS}
    SENTRY_CAPTURE_WARNINGS: 0
    REGION: ${opt:region}
    REQUESTS_TABLE: ${opt:stage}-requests
    REDIS_CACHE_ADDR:
      Fn::If:
        - CreateRedis
        - Fn::GetAtt:
          - SchedulingCache
          - RedisEndpoint.Address
        - Fn::ImportValue: common-redis-scheduler-address
    REDIS_CACHE_PORT:
      Fn::If:
        - CreateRedis
        - Fn::GetAtt:
          - SchedulingCache
          - RedisEndpoint.Port
        - Fn::ImportValue: common-redis-scheduler-port
    REDIS_DATABASE: 0
    REDIS_QUEUE_NAME: schedule-v2-${opt:stage}

functions:
  list:
    handler: src/handlers/list.handler
    memory: 512
    timeout: 30
  remove:
    handler: src/handlers/remove.handler
    memory: 512
    timeout: 30
    environment:
      FALLBACK_REDIS_QUEUE_NAME:
        Fn::If:
          - CreateRedis
          - Fn::If:
            - IsProduction
            - schedule # production uses this queue-name
            - schedule-${opt:stage} # environment specific queue-name which will use same redis service
          - schedule-${opt:stage}
  add:
    handler: src/handlers/add.handler
    memory: 512

    timeout: 30
  extend:
    handler: src/handlers/extend.handler
    memory: 512
    timeout: 30
  flush-all:
    handler: src/handlers/flush.handler
    memory: 512
    timeout: 30

  scheduler:
    handler: src/handlers/generalScheduler.handler
    memory: 512
    timeout: 120
    environment:
      EXPIRED_FUNCTION_NAME: new-request-${opt:stage}-expireRequest
      PURGE_REQUEST_FUNCTION_NAME: new-request-${opt:stage}-purgeRequest
      SEND_REQUEST_FUNCTION_NAME: new-request-${opt:stage}-sendRequest
      MARK_FOLLOWUP_FUNCTION_NAME: new-request-${opt:stage}-markToFollowUpRequest
      UNMARK_FOLLOWUP_FUNCTION_NAME: new-request-${opt:stage}-unmarkToFollowUpRequest
      NOTIFY_EXPIRING_FUNCTION_NAME: new-request-${opt:stage}-notifyExpiring
    events:
      - schedule: rate(1 hour)

resources:
  Conditions:
    IsProduction:
      Fn::Equals:
        - ${opt:stage}
        - quote
    FoundCommonRedis:
      Fn::Equals:
        - ${file(./serverless.js):foundCommonRedis}
        - true
    CreateCommonRedis:
      Fn::And:
        - Fn::Or:
          - Condition: IsCommonRedisCacheOwner
          - Fn::Not:
            - Condition: FoundCommonRedis
        - Fn::Not:
          - Condition: IsProduction
    IsCommonRedisCacheOwner:
      Fn::Equals:
        - ${file(./serverless.js):isCommonRedisCacheOwner}
        - true
    CreateRedis:
      Fn::Or:
        - Condition: IsProduction
        - Condition: CreateCommonRedis
  Resources:
    SchedulingCacheSubnetGroup:
      Condition: CreateRedis
      Type: AWS::ElastiCache::SubnetGroup
      Properties:
        CacheSubnetGroupName: ${self:service}-${opt:stage}
        Description: "Subnet for the Cache"
        SubnetIds:
          - ${opt:subnetPrivateId}
    SchedulingCache:
      Condition: CreateRedis
      Type: AWS::ElastiCache::CacheCluster
      Properties:
        ClusterName: ${opt:stage}-redis-cache
        CacheNodeType: cache.t2.micro
        Engine: redis
        NumCacheNodes: 1
        VpcSecurityGroupIds:
          - ${opt:securityGroupId}
        CacheSubnetGroupName:
          Ref: SchedulingCacheSubnetGroup
  Outputs:
    CommonSecurityGroupId:
      Condition: CreateCommonRedis
      Description: The common used redis securityGroupId
      Value: ${opt:securityGroupId}
      Export:
        Name: common-redis-scheduler-security-group
    CommonSubnetPrivateId:
      Condition: CreateCommonRedis
      Description: The common used redis SubnetPrivateId
      Value: ${opt:subnetPrivateId}
      Export:
        Name: common-redis-scheduler-subnet-private
    CommonRedisCacheAddr:
      Condition: CreateCommonRedis
      Description: Common used redis cache address
      Value:
        "Fn::GetAtt": [ SchedulingCache, RedisEndpoint.Address ]
      Export:
        Name: common-redis-scheduler-address
    CommonRedisCachePort:
      Condition: CreateCommonRedis
      Description: Common used redis cache port
      Value:
        "Fn::GetAtt": [ SchedulingCache, RedisEndpoint.Port ]
      Export:
        Name: common-redis-scheduler-port
